.screenshot-monitor {
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin: 16px 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  max-height: calc(100vh - 120px); /* Ensure it doesn't exceed viewport minus header */
  overflow-y: auto; /* Enable scrolling within the component if needed */
  color: #212529; /* Explicit dark text color */
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid #dee2e6;
}

.monitor-header h3 {
  margin: 0;
  color: #212529; /* Darker color for better visibility */
  font-size: 18px;
  font-weight: 600;
}

.monitor-controls {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

.monitor-toggle {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  color: white; /* Ensure button text is white */
}

.monitor-toggle:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.monitor-toggle.active {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
  }
  50% {
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.5);
  }
  100% {
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
  }
}

.monitor-status {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
  padding: 12px;
  background-color: white;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  color: #212529; /* Explicit text color */
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #212529; /* Darker color for better visibility */
}

.status-item strong {
  color: #212529; /* Ensure strong text is visible */
}

.status-icon {
  font-size: 16px;
  min-width: 20px;
}

.status-text {
  flex: 1;
  color: #212529; /* Explicit color */
}

.monitor-error {
  margin: 12px 0;
  padding: 12px;
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  border-radius: 6px;
  font-size: 14px;
}

.monitor-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 20px;
}

.info-item {
  padding: 12px;
  background-color: white;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  color: #212529; /* Explicit text color */
}

.info-item strong {
  color: #212529; /* Darker color for headings */
  font-size: 14px;
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
}

.info-item ul {
  margin: 0;
  padding-left: 20px;
  color: #495057; /* Darker gray for better readability */
  font-size: 13px;
  line-height: 1.5;
}

.info-item li {
  margin-bottom: 4px;
  color: #495057; /* Explicit color for list items */
}

.info-item code {
  background-color: #e9ecef;
  color: #212529; /* Dark text on light background */
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  font-weight: 500;
}

.similarity-toggle {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #495057; /* Darker gray for better visibility */
  cursor: pointer;
  user-select: none;
}

.similarity-toggle input[type="checkbox"] {
  margin: 0;
  transform: scale(0.9);
}

.similarity-toggle input[type="checkbox"]:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.similarity-toggle span {
  white-space: nowrap;
  color: #495057; /* Explicit color */
}

/* Responsive design */
@media (max-width: 768px) {
  .screenshot-monitor {
    padding: 16px;
    margin: 12px 0;
  }
  
  .monitor-header {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }
  
  .monitor-status {
    gap: 6px;
  }
  
  .status-item {
    font-size: 13px;
  }
  
  .info-item {
    padding: 10px;
  }
}

/* Dark mode override protection */
@media (prefers-color-scheme: dark) {
  .screenshot-monitor {
    background-color: #f8f9fa !important;
    color: #212529 !important;
  }
  
  .monitor-status {
    background-color: white !important;
    color: #212529 !important;
  }
  
  .info-item {
    background-color: white !important;
    color: #212529 !important;
  }
  
  .status-item {
    color: #212529 !important;
  }
  
  .info-item ul {
    color: #495057 !important;
  }
  
  .info-item li {
    color: #495057 !important;
  }
  
  .similarity-toggle {
    color: #495057 !important;
  }
  
  .similarity-toggle span {
    color: #495057 !important;
  }
}

.select-apps-button {
  background-color: #17a2b8;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.select-apps-button:hover:not(:disabled) {
  background-color: #138496;
}

.select-apps-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.selected-sources-info {
  background-color: #17a2b8;
  color: white;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 14px;
  margin-right: 8px;
} 