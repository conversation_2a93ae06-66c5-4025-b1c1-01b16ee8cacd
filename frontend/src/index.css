* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Global text overflow protection */
* {
  word-break: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

/* Prevent horizontal overflow globally */
html {
  overflow-x: hidden;
}

body {
  overflow-x: hidden;
}

/* Exception for pre elements and code blocks */
pre, code {
  word-break: normal;
  overflow-wrap: normal;
  hyphens: none;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
  color: #333;
  height: 100vh;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

#root {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Global scrollbar styling - lower specificity */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Ensure components can override scrollbar styles */
.memory-tree-visualization,
.existing-memory,
.chat-window {
  /* Component-specific scrollbar rules take precedence */
}

/* Selection styling */
::selection {
  background-color: #007acc;
  color: white;
}

/* Focus outline */
*:focus {
  outline: 2px solid #007acc;
  outline-offset: 2px;
} 