{"name": "mirix-desktop", "version": "0.1.2", "description": "MIRIX Desktop Application", "main": "public/electron.js", "homepage": "./", "private": true, "dependencies": {"@electric-sql/pglite": "^0.3.4", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.4.0", "electron-is-dev": "^2.0.0", "express": "^4.18.2", "node-screenshots": "^0.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^8.0.7", "react-scripts": "5.0.1", "react-syntax-highlighter": "^15.5.0", "reactflow": "^11.11.4", "rehype-katex": "^7.0.1", "remark-math": "^6.0.0", "screenshot-desktop": "^1.15.1", "web-vitals": "^2.1.4", "i18next": "^23.11.5", "react-i18next": "^13.5.0", "i18next-browser-languagedetector": "^7.2.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "electron": "electron .", "electron-dev": "concurrently \"npm start\" \"wait-on http://localhost:3000 && electron .\"", "electron-pack": "npm run build && npm run copy-backend && electron-builder", "copy-backend": "node scripts/copy-prebuilt-backend.js", "test-dmg-direct": "node scripts/test-dmg-direct.js", "test-backend-logging": "node scripts/test-backend-logging.js", "test-loading-modal": "node scripts/test-loading-modal.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"concurrently": "^7.6.0", "electron": "^25.3.0", "electron-builder": "^24.6.3", "wait-on": "^7.0.1"}, "build": {"appId": "com.mirix.desktop", "productName": "MIRIX", "directories": {"output": "dist"}, "files": ["build/**/*", "node_modules/**/*", "public/electron.js", "public/preload.js"], "extraResources": [{"from": "backend/main", "to": "backend/main"}, {"from": "backend/configs/", "to": "backend/configs/", "filter": ["**/*"]}], "asarUnpack": ["**/node_modules/@electric-sql/pglite/**/*", "**/node_modules/@electric-sql/pglite/dist/**/*", "**/node_modules/@electric-sql/pglite/dist/pglite.data", "**/node_modules/@electric-sql/pglite/dist/pglite.wasm"], "mac": {"category": "public.app-category.productivity", "target": "dmg", "icon": "public/icon.png"}, "win": {"target": "nsis", "icon": "public/icon.png"}, "linux": {"target": "AppImage", "icon": "public/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}}